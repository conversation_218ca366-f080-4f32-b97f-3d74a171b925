import unittest
from unittest.mock import Mock, patch, MagicMock
import datetime
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Mock Flask g object before importing anything that uses it
class MockG:
    def __init__(self):
        self.db_session = Mock()
        self.client_db_session = Mock()
        self.user_id = "test-user"
        self.tenant_id = "test-tenant"

# Create a mock flask module
mock_flask = Mock()
mock_flask.g = MockG()
sys.modules['flask'] = mock_flask

# Mock the app module to prevent configuration loading
mock_app = Mock()
mock_app.config = {'ENVIRONMENT': 'test'}
sys.modules['app'] = mock_app

# Now we can safely import our modules
from api.common.notification import NotificationFunction


class TestNotificationBatchSystem(unittest.TestCase):
    """Test the optimized batch notification system."""

    def setUp(self):
        """Set up test fixtures."""
        self.notification_func = NotificationFunction()

        # Use the mocked g object
        self.mock_db_session = mock_flask.g.db_session
        
        # Mock services
        self.mock_google_fcm = Mock()
        self.mock_email_service = Mock()
        
        # Create test notification
        self.test_notification = Mock(spec=Notification)
        self.test_notification.id = "test-notification-id"
        self.test_notification.client_id = "test-client-id"
        self.test_notification.recipient_type = "all"
        self.test_notification.title = "Test Notification"
        self.test_notification.body = "Test notification body"
        self.test_notification.data = {"test": "data"}
        self.test_notification.enable_notification_push = True
        self.test_notification.enable_email_notification = True
        self.test_notification.update_badge = True

    def test_bulk_fetch_user_data_success(self):
        """Test bulk fetching of user data works correctly."""
        # Mock users
        mock_user1 = Mock(spec=User)
        mock_user1.id = "user1"
        mock_user1.email = "<EMAIL>"
        mock_user1.first_name = "User"
        mock_user1.settings = {"notification_preference": {"all": True, "email_all": True}}
        
        mock_user2 = Mock(spec=User)
        mock_user2.id = "user2"
        mock_user2.email = "<EMAIL>"
        mock_user2.first_name = "User"
        mock_user2.settings = None
        
        # Mock user tokens
        mock_token1 = Mock(spec=UserToken)
        mock_token1.user_id = "user1"
        mock_token1.device_token = "token1"
        mock_token1.device_type = "android"
        mock_token1.device_id = "device1"
        
        mock_token2 = Mock(spec=UserToken)
        mock_token2.user_id = "user2"
        mock_token2.device_token = "token2"
        mock_token2.device_type = "ios"
        mock_token2.device_id = "device2"
        
        # Setup mock queries
        user_query = Mock()
        user_query.filter.return_value.all.return_value = [mock_user1, mock_user2]
        self.mock_db_session.query.return_value = user_query
        
        token_query = Mock()
        token_query.filter.return_value.all.return_value = [mock_token1, mock_token2]
        
        # Configure query to return different results based on model
        def query_side_effect(model):
            if model == User:
                return user_query
            elif model == UserToken:
                return token_query
            return Mock()
        
        self.mock_db_session.query.side_effect = query_side_effect
        
        # Test bulk fetch
        user_ids = ["user1", "user2"]
        result = self.notification_func._bulk_fetch_user_data(user_ids)
        
        # Verify results
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]['id'], "user1")
        self.assertEqual(result[0]['user_wants_push'], True)
        self.assertEqual(result[0]['user_wants_email'], True)
        self.assertEqual(len(result[0]['tokens']), 1)
        
        self.assertEqual(result[1]['id'], "user2")
        self.assertEqual(result[1]['user_wants_push'], True)  # Default value
        self.assertEqual(result[1]['user_wants_email'], True)  # Default value
        self.assertEqual(len(result[1]['tokens']), 1)

    def test_send_notification_to_single_user_success(self):
        """Test sending notification to a single user works correctly."""
        # Mock user data
        user_data = {
            'id': 'test-user',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'user_wants_push': True,
            'user_wants_email': True,
            'tokens': [{
                'device_token': 'test-token',
                'device_type': 'android',
                'device_id': 'test-device'
            }]
        }
        
        # Mock successful FCM and email sending
        self.mock_google_fcm.push_notification.return_value = True
        self.mock_email_service.send_notification_email.return_value = True
        
        # Mock get_unread_count
        with patch.object(self.notification_func, 'get_unread_count', return_value=5):
            result = self.notification_func._send_notification_to_single_user(
                self.test_notification, user_data, self.mock_google_fcm, self.mock_email_service
            )
        
        # Verify success
        self.assertTrue(result)
        
        # Verify FCM was called
        self.mock_google_fcm.push_notification.assert_called_once()
        
        # Verify email was called
        self.mock_email_service.send_notification_email.assert_called_once()

    def test_process_user_batch_handles_failures(self):
        """Test that batch processing handles individual user failures gracefully."""
        user_ids = ["user1", "user2", "user3"]
        
        # Mock bulk fetch to return user data
        mock_users_data = [
            {'id': 'user1', 'email': '<EMAIL>', 'first_name': 'User1', 
             'user_wants_push': True, 'user_wants_email': True, 'tokens': []},
            {'id': 'user2', 'email': '<EMAIL>', 'first_name': 'User2', 
             'user_wants_push': True, 'user_wants_email': True, 'tokens': []},
            {'id': 'user3', 'email': '<EMAIL>', 'first_name': 'User3', 
             'user_wants_push': True, 'user_wants_email': True, 'tokens': []}
        ]
        
        with patch.object(self.notification_func, '_bulk_fetch_user_data', return_value=mock_users_data):
            # Mock _send_notification_to_single_user to simulate mixed success/failure
            def mock_send_single_user(notification, user_data, fcm, email):
                if user_data['id'] == 'user2':
                    raise Exception("Simulated failure")
                return user_data['id'] != 'user3'  # user1: success, user2: exception, user3: failure
            
            with patch.object(self.notification_func, '_send_notification_to_single_user', side_effect=mock_send_single_user):
                success_count, failure_count = self.notification_func._process_user_batch(
                    self.test_notification, user_ids, self.mock_google_fcm, self.mock_email_service
                )
        
        # Verify results: 1 success (user1), 2 failures (user2 exception, user3 returned False)
        self.assertEqual(success_count, 1)
        self.assertEqual(failure_count, 2)

    def test_batch_size_configuration(self):
        """Test that batch processing respects batch size configuration."""
        # Mock total user count
        mock_query = Mock()
        mock_query.filter.return_value.count.return_value = 250  # 250 total users
        mock_query.filter.return_value.offset.return_value.limit.return_value.all.return_value = [
            ("user1",), ("user2",), ("user3",)  # Simulate batch of user IDs
        ]
        self.mock_db_session.query.return_value = mock_query
        
        # Mock process_user_batch to track calls
        with patch.object(self.notification_func, '_process_user_batch', return_value=(3, 0)) as mock_process:
            # Test with custom batch size
            result = self.notification_func._send_notification_to_all_users_batched(
                self.test_notification, self.mock_google_fcm, self.mock_email_service, batch_size=50
            )
            
            # Should be called multiple times for 250 users with batch size 50
            # Note: The exact number depends on the mock setup, but we verify it was called
            self.assertTrue(mock_process.called)
            self.assertTrue(result)

    @patch('api.common.notification.logger')
    def test_error_logging(self, mock_logger):
        """Test that errors are properly logged."""
        # Mock database error
        self.mock_db_session.query.side_effect = Exception("Database connection error")
        
        # Test bulk fetch with error
        result = self.notification_func._bulk_fetch_user_data(["user1"])
        
        # Verify error was logged and empty result returned
        mock_logger.error.assert_called()
        self.assertEqual(result, [])


if __name__ == '__main__':
    unittest.main()
